import { Component, input, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ProcessedEvent } from '../../models/message.model';

@Component({
  selector: 'app-activity-timeline',
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './activity-timeline.html',
  styleUrl: './activity-timeline.scss'
})
export class ActivityTimeline {
  // Signal inputs
  events = input<ProcessedEvent[]>([]);
  isLoading = input<boolean>(false);

  // Internal signals
  isExpanded = signal<boolean>(true);

  // Computed signals
  hasEvents = computed(() => this.events().length > 0);
  eventCount = computed(() => this.events().length);

  toggleExpansion(): void {
    this.isExpanded.update(expanded => !expanded);
  }

  getIconForEvent(title: string): string {
    switch (title.toLowerCase()) {
      case 'generating search queries':
        return 'search';
      case 'web research':
        return 'language';
      case 'reflection':
        return 'psychology';
      case 'finalizing answer':
        return 'edit';
      default:
        return 'info';
    }
  }

  getColorForEvent(title: string): string {
    switch (title.toLowerCase()) {
      case 'generating search queries':
        return 'primary';
      case 'web research':
        return 'accent';
      case 'reflection':
        return 'warn';
      case 'finalizing answer':
        return 'primary';
      default:
        return 'primary';
    }
  }
}
