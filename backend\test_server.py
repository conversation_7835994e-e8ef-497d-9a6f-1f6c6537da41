#!/usr/bin/env python3
"""
Simple test script to run the FastAPI server with CORS configuration.
This script can be used to test if the CORS configuration is working properly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from agent.app import app
    import uvicorn
    
    if __name__ == "__main__":
        print("Starting FastAPI server with CORS configuration...")
        print("Server will be available at: http://127.0.0.1:2024")
        print("CORS origins configured for Angular, React, and other development servers")
        print("Press Ctrl+C to stop the server")
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=2024,
            reload=False,
            log_level="info"
        )
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the backend directory")
    sys.exit(1)
except Exception as e:
    print(f"Error starting server: {e}")
    sys.exit(1)
