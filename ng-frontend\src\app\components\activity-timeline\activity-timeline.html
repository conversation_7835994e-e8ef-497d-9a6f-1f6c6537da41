@if (hasEvents() || isLoading()) {
  <div class="activity-timeline">
    <mat-expansion-panel
      [expanded]="isExpanded()"
      class="timeline-panel">

      <mat-expansion-panel-header (click)="toggleExpansion()">
        <mat-panel-title>
          <mat-icon>timeline</mat-icon>
          Research Activity
          @if (eventCount() > 0) {
            <span class="event-count">({{ eventCount() }})</span>
          }
        </mat-panel-title>
        <mat-panel-description>
          @if (isLoading()) {
            <span>Processing...</span>
            <mat-spinner diameter="20"></mat-spinner>
          } @else {
            <span>Completed</span>
          }
        </mat-panel-description>
      </mat-expansion-panel-header>

      <div class="timeline-content">
        @if (events().length > 0) {
          <div class="timeline-events">
            @for (event of events(); track $index) {
              <div class="timeline-event">
                <div class="event-icon">
                  <mat-icon [color]="getColorForEvent(event.title)">
                    {{ getIconForEvent(event.title) }}
                  </mat-icon>
                </div>
                <div class="event-content">
                  <h4 class="event-title">{{ event.title }}</h4>
                  <p class="event-data">{{ event.data }}</p>
                  @if (event.timestamp) {
                    <span class="event-time">
                      {{ event.timestamp | date:'short' }}
                    </span>
                  }
                </div>
              </div>
            }
          </div>
        }

        @if (isLoading()) {
          <div class="loading-indicator">
            <mat-spinner diameter="24"></mat-spinner>
            <span>Processing your request...</span>
          </div>
        }
      </div>
    </mat-expansion-panel>
  </div>
}
