.activity-timeline {
  margin: 1rem 0;

  .timeline-panel {
    background: rgba(48, 48, 48, 0.8);
    border-radius: 0.5rem;
    backdrop-filter: blur(10px);

    .mat-expansion-panel-header {
      padding: 1rem;

      .mat-panel-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #ffffff;
        font-weight: 500;

        .event-count {
          color: #b0b0b0;
          font-size: 0.875rem;
          font-weight: 400;
        }
      }

      .mat-panel-description {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #b0b0b0;
      }
    }

    .timeline-content {
      padding: 0 1rem 1rem 1rem;

      .timeline-events {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .timeline-event {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 0.75rem;
          background: rgba(64, 64, 64, 0.5);
          border-radius: 0.5rem;
          border-left: 3px solid #2196f3;

          .event-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(33, 150, 243, 0.1);
            border-radius: 50%;
            flex-shrink: 0;

            mat-icon {
              font-size: 1.25rem;
              width: 1.25rem;
              height: 1.25rem;
            }
          }

          .event-content {
            flex: 1;
            min-width: 0;

            .event-title {
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
              font-weight: 500;
              color: #ffffff;
            }

            .event-data {
              margin: 0 0 0.5rem 0;
              font-size: 0.875rem;
              color: #e0e0e0;
              line-height: 1.4;
              word-wrap: break-word;
            }

            .event-time {
              font-size: 0.75rem;
              color: #b0b0b0;
              font-style: italic;
            }
          }
        }
      }

      .loading-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        padding: 2rem;
        color: #b0b0b0;
        font-style: italic;
      }
    }
  }
}

// Event type specific colors
.timeline-event {
  &.generating {
    border-left-color: #4caf50;

    .event-icon {
      background: rgba(76, 175, 80, 0.1);
    }
  }

  &.research {
    border-left-color: #ff9800;

    .event-icon {
      background: rgba(255, 152, 0, 0.1);
    }
  }

  &.reflection {
    border-left-color: #9c27b0;

    .event-icon {
      background: rgba(156, 39, 176, 0.1);
    }
  }

  &.finalizing {
    border-left-color: #2196f3;

    .event-icon {
      background: rgba(33, 150, 243, 0.1);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .activity-timeline {
    .timeline-panel {
      .timeline-content {
        .timeline-events {
          .timeline-event {
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 0.75rem;

            .event-content {
              .event-data {
                text-align: left;
              }
            }
          }
        }
      }
    }
  }
}
