.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);

  .welcome-content {
    max-width: 800px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 2rem;

    .welcome-header {
      .welcome-title {
        font-size: 4rem;
        font-weight: 600;
        color: #ffffff;
        margin: 0 0 1rem 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .welcome-subtitle {
        font-size: 1.5rem;
        color: #b0b0b0;
        margin: 0;
        font-weight: 300;
      }
    }

    .input-section {
      margin: 2rem 0;
    }

    .powered-by {
      font-size: 0.875rem;
      color: #666666;
      margin: 0;
      font-style: italic;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .welcome-container {
    padding: 1rem;

    .welcome-content {
      .welcome-header {
        .welcome-title {
          font-size: 2.5rem;
        }

        .welcome-subtitle {
          font-size: 1.25rem;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .welcome-container {
    .welcome-content {
      .welcome-header {
        .welcome-title {
          font-size: 2rem;
        }

        .welcome-subtitle {
          font-size: 1rem;
        }
      }
    }
  }
}
