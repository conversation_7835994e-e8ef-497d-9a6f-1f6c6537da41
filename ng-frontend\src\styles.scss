/* Angular Material Prebuilt Theme */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Global Styles */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #1a1a1a;
  color: #ffffff;
  overflow: hidden;
}

body {
  font-size: 14px;
  line-height: 1.5;
}

// Custom scrollbar styles
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

// Material Design overrides for dark theme
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: rgba(255, 255, 255, 0.3);
  }

  &.mat-focused {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: #2196f3;
    }
  }
}

.mat-mdc-select-panel {
  background: #424242;
}

.mat-mdc-option {
  color: #ffffff;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &.mdc-list-item--selected {
    background: rgba(33, 150, 243, 0.2);
  }
}
