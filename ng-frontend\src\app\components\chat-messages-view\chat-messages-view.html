<div class="chat-container">
  <div class="messages-area">
    @for (message of messages(); track message.id; let i = $index) {
      <div class="message-wrapper">
        @if (message.type === 'human') {
          <!-- Human Message Bubble -->
          <div class="message human-message">
            <mat-card class="message-card human-card">
              <mat-card-content>
                <div class="message-content">
                  {{ message.content }}
                </div>
                @if (message.timestamp) {
                  <div class="message-time">
                    {{ message.timestamp | date:'short' }}
                  </div>
                }
              </mat-card-content>
            </mat-card>
          </div>
        } @else {
          <!-- AI Message Bubble -->
          <div class="message ai-message">
            <mat-card class="message-card ai-card">
              <mat-card-header>
                <div class="ai-header">
                  <mat-icon>smart_toy</mat-icon>
                  <span>AI Assistant</span>
                  <button
                    mat-icon-button
                    (click)="copyToClipboard(message.content)"
                    class="copy-button"
                    matTooltip="Copy to clipboard">
                    <mat-icon>content_copy</mat-icon>
                  </button>
                </div>
              </mat-card-header>

              <mat-card-content>
                <div class="message-content">
                  {{ message.content }}
                </div>
                @if (message.timestamp) {
                  <div class="message-time">
                    {{ message.timestamp | date:'short' }}
                  </div>
                }
              </mat-card-content>

              <!-- Activity Timeline for AI messages -->
              @if (message.id) {
                <div class="activity-section">
                  @if (isLastMessage(i) && isLoading()) {
                    <!-- Show live activity for the last message while loading -->
                    <app-activity-timeline
                      [events]="liveActivityEvents()"
                      [isLoading]="isLoading()">
                    </app-activity-timeline>
                  } @else {
                    <!-- Show historical activity for completed messages -->
                    @if (getActivityEventsForMessage(message.id).length > 0) {
                      <app-activity-timeline
                        [events]="getActivityEventsForMessage(message.id)"
                        [isLoading]="false">
                      </app-activity-timeline>
                    }
                  }
                </div>
              }
            </mat-card>
          </div>
        }
      </div>
    }

    <!-- Loading indicator for new messages -->
    @if (isLoading() && messages().length > 0) {
      <div class="message-wrapper">
        <div class="message ai-message">
          <mat-card class="message-card ai-card loading-card">
            <mat-card-content>
              <div class="loading-content">
                <mat-icon class="loading-icon">smart_toy</mat-icon>
                <span>AI is thinking...</span>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    }
  </div>

  <!-- Input Form -->
  <div class="input-area">
    <app-input-form
      [isLoading]="isLoading()"
      [hasHistory]="true"
      (submitQuery)="onSubmit($event)"
      (cancel)="onCancel()"
      (newSearch)="onNewSearch()">
    </app-input-form>
  </div>
</div>
